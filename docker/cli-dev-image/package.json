{"name": "@sage/xtrem-dev-cli", "version": "58.0.29", "buildStamp": "2025-08-19T20:11:12.188Z", "description": "", "main": "node_modules/.bin/xtrem", "scripts": {}, "keywords": [], "author": "", "license": "UNLICENSED", "dependencies": {"@sage/xtrem-cli": "^58.0.29", "@sage/xtrem-cli-dev": "^58.0.29", "@sage/xtrem-cli-dev-data": "^58.0.29", "@sage/xtrem-cli-atp": "^58.0.29", "@sage/xtrem-cli-main": "^58.0.29"}, "pnpm": {"overrides": {"@sage/design-tokens": "4.35.0", "@puppeteer/browsers@1.3.0>tar-fs": "^3.0.0", "@turbo/codemod@1>axios": "^1.0.0", "@types/react": "^18.3.3", "@wdio/globals": "8.12.1", "d3-color": "^3.1.0", "graphql": "^16.11.0", "graphql-yoga": "^5.14.0", "react-dom": "^18.3.1", "react": "^18.3.1", "sinon": "^21.0.0", "styled-components": "^5.3.11", "typescript": "~5.9.0", "webdriverio": "8.12.1", "webpack": "^5.95.0", "puppeteer-core>ws": "^8.17.1", "@cucumber/cucumber>semver": "^7.5.2", "pbkdf2": "^3.1.3", "@newrelic/security-agent>form-data": "^4.0.0"}, "peerDependencyRules": {"allowAny": ["redux"], "allowedVersions": {"codemirror-graphql@2>@codemirror/language": "6"}, "ignoreMissing": ["ckeditor5"]}, "patchedDependencies": {"react-grid-layout": "patches/<EMAIL>", "@types/react-grid-layout": "patches/@<EMAIL>", "@ag-grid-community/core": "patches/@ag-grid-community__core.patch", "carbon-react@153.7.0": "patches/<EMAIL>", "jsdom": "patches/jsdom.patch"}, "onlyBuiltDependencies": ["@contrast/fn-inspect", "@newrelic/native-metrics", "@parcel/watcher", "@sage/bms-dashboard", "@sage/xdev", "@swc/core", "canvas", "esbuild", "nx", "oracledb", "protobufjs", "puppeteer", "re2", "sharp"]}, "packageManager": "pnpm@10.15.0"}