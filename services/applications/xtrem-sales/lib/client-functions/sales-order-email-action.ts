import { SalesOrder as SalesOrderNode, SalesOrderStatus } from '@sage/xtrem-sales-api';
import * as ui from '@sage/xtrem-ui';
import { SalesOrder } from '../pages/sales-order';

function getLocalizeOrderSendAction(orderStatus: SalesOrderStatus) {
    if (orderStatus === 'quote') {
        return {
            title: ui.localize(
                '@sage/xtrem-sales/pages__main_list_sales_quote__section_title_send_sales_order_dialog_',
                'Send sales quote',
            ),
            confirmTitle: ui.localize(
                '@sage/xtrem-sales/pages__sales_quote__send_sales_order_dialog_title',
                'Sales quote email send confirmation',
            ),
            confirmMessage: ui.localize(
                '@sage/xtrem-sales/pages__sales_quote__send_sales_order_dialog_content',
                'You are about to send the sales quote email.',
            ),
            resultMessage: ui.localize('@sage/xtrem-sales/pages__sales_quote__email_sent', 'Sales quote sent to: '),
        };
    }
    return {
        title: ui.localize(
            '@sage/xtrem-sales/pages__main_list_sales_order_section_title__send_sales_order_dialog',
            'Send sales order',
        ),
        confirmTitle: ui.localize(
            '@sage/xtrem-sales/pages__sales_order__send_sales_order_dialog_title',
            'Sales order email send confirmation',
        ),
        confirmMessage: ui.localize(
            '@sage/xtrem-sales/pages__sales_order__send_sales_order_dialog_content',
            'You are about to send the sales order email.',
        ),
        resultMessage: ui.localize('@sage/xtrem-sales/pages__sales_order__email_sent', 'Sales order sent to: '),
    };
}

export async function sendOrderAndEmail(orderPage: SalesOrder, rowItem?: SalesOrderNode) {
    const orderStatus = (rowItem?.status ?? orderPage.status.value) as SalesOrderStatus;

    const contact = (rowItem?.soldToContact ?? orderPage.soldToContact) as SalesOrderNode['soldToContact'];

    const nodeName = '@sage/xtrem-sales/SalesOrder';

    const address = rowItem?.soldToLinkedAddress?._id ?? orderPage.soldToLinkedAddress?.value?._id ?? '';

    const _id = rowItem?._id ?? orderPage.$.recordId;

    const dialogTexts = JSON.stringify(getLocalizeOrderSendAction(orderStatus));

    await this.$.dialog.page(
        '@sage/xtrem-master-data/SendEmailPanel',
        { _id, nodeName, contact, address, dialogTexts },
        { resolveOnCancel: true, size: 'extra-large' },
    );
}
