import type { Context, Logger } from '@sage/xtrem-core';
import { BusinessRuleError, NodeStatus, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import { loggers } from '.';
import * as xtremSales from '../index';
import {
    getCurrentHeaderNote,
    getCurrentLineNote,
    setHeaderExternalNote,
    setHeaderInternalNote,
    setHeaderSetupNote,
    setLineExternalNote,
    setLineInternalNote,
} from './common';

/**
 * Calculates the display status of the shipment based on the other document status properties
 * @param status
 * @param invoiceStatus
 * @param stockTransactionStatus
 * @returns display status enum
 */
export function calculateSalesShipmentDisplayStatus(
    status: xtremSales.enums.SalesShipmentStatus,
    invoiceStatus: xtremSales.enums.SalesDocumentInvoiceStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
): xtremSales.enums.SalesShipmentDisplayStatus {
    if (stockTransactionStatus === 'error') {
        return 'error';
    }
    if (stockTransactionStatus === 'inProgress') {
        return 'postingInProgress';
    }
    if (invoiceStatus === 'invoiced') {
        return 'invoiced';
    }
    if (invoiceStatus === 'partiallyInvoiced') {
        return 'partiallyInvoiced';
    }
    if (stockTransactionStatus === 'completed') {
        return 'shipped';
    }
    if (status === 'shipped') {
        return 'shipped';
    }
    if (status === 'readyToShip') {
        return 'readyToShip';
    }
    return 'readyToProcess';
}

export function linkedOrderArray(instance: xtremSales.nodes.SalesShipment): Promise<xtremSales.nodes.SalesOrder[]> {
    return instance.$.context
        .query(xtremSales.nodes.SalesOrder, {
            filter: { _id: { _in: Object.keys(instance.__salesOrders) } },
            forUpdate: true,
        })
        .toArray();
}

export async function updateHeaderNotesOnCreation(instance: xtremSales.nodes.SalesShipment) {
    if (instance.$.status === NodeStatus.added) {
        const salesOrderArray = await linkedOrderArray(instance);
        if (salesOrderArray.length === 1) {
            const isTransferHeaderNote = await salesOrderArray[0].isTransferHeaderNote;

            if (isTransferHeaderNote) {
                const currentNote = await getCurrentHeaderNote(instance);
                await setHeaderInternalNote(instance, salesOrderArray, currentNote);
                await setHeaderExternalNote(instance, salesOrderArray, currentNote);
            }
            await setHeaderSetupNote(instance, isTransferHeaderNote, await salesOrderArray[0].isTransferLineNote);
        }
    }
}

export async function updateLineNotesOnCreation(instance: xtremSales.nodes.SalesShipmentLine) {
    if (instance.$.status === NodeStatus.added && (await instance.salesOrderLines.length)) {
        const linkedDocument = await (await instance.salesOrderLines.elementAt(0)).linkedDocument;
        if (await (await linkedDocument.document).isTransferLineNote) {
            const currentNote = await getCurrentLineNote(instance);
            await setLineInternalNote(instance, linkedDocument, currentNote);
            await setLineExternalNote(instance, linkedDocument, currentNote);
        }
    }
}
export async function getInvoiceInstance(
    context: Context,
    shipment: xtremSales.nodes.SalesShipment,
): Promise<xtremSales.nodes.SalesInvoice | null> {
    const salesInvoiceCreator = xtremSales.nodes.SalesShipment.instantiateSalesInvoicesCreator(context, date.today());

    await shipment.lines.forEach(async salesDocumentLine => {
        await salesInvoiceCreator.prepareNodeCreateData([{ salesDocumentLine }]);
    });

    if (Object.keys(salesInvoiceCreator.salesOutputDocuments).length === 1) {
        const invoicePayload =
            salesInvoiceCreator.salesOutputDocuments[Object.keys(salesInvoiceCreator.salesOutputDocuments)[0]];
        return context.create(xtremSales.nodes.SalesInvoice, invoicePayload, { isTransient: true });
    }
    return null;
}

async function checkAndSetLineStatusShipped(salesShipment: xtremSales.nodes.SalesShipment) {
    await salesShipment.lines
        .filter(async line => (await line.stockTransactionStatus) === 'completed')
        .forEach(line => line.$.set({ status: 'shipped' }));
}

export async function resynchronizeShipmentStatus(salesShipment: xtremSales.nodes.SalesShipment, logger: Logger) {
    await checkAndSetLineStatusShipped(salesShipment);
    if ((await salesShipment.status) !== 'shipped' && (await salesShipment.stockTransactionStatus) === 'completed') {
        await salesShipment.$.set({ status: 'shipped' });

        await logger.verboseAsync(
            async () => `Sales shipment ${await salesShipment.number} has been set to 'Shipped'.`,
        );
    }
    await salesShipment.$.save();
}

export async function validateShipmentLinesForInvoicing(
    context: Context,
    salesDocument: xtremSales.nodes.SalesShipment,
): Promise<void> {
    const hasInvalidLines =
        (
            await context
                .query(xtremSales.nodes.SalesShipmentLine, {
                    filter: {
                        document: salesDocument._id,
                        _or: [{ invoiceStatus: 'invoiced' }, { stockTransactionStatus: { _ne: 'completed' } }],
                    },
                    first: 1,
                })
                .toArray()
        ).length > 0;

    if (hasInvalidLines) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/nodes__sales_shipment__create_sales_invoices_from_shipments__already_invoiced_or_not_posted',
                'All shipment lines must not be invoiced and must be posted.',
            ),
        );
    }
}

export async function controlReadyToShip(salesShipment: xtremSales.nodes.SalesShipment, isSafeToRetry: boolean) {
    if ((await salesShipment.status) !== 'readyToShip') {
        const { context } = salesShipment.$;
        xtremMasterData.functions.throwErrorOrLogWarn({
            errorMessage: context.localize(
                '@sage/xtrem-sales/cant_post_shipment_when_status_is_not_ready_to_ship',
                'The status is not {{readyToShipStatus}}, the shipment cannot be posted.',
                {
                    readyToShipStatus: context.localizeEnumMember(
                        '@sage/xtrem-sales/SalesShipmentStatus',
                        'readyToShip',
                    ),
                },
            ),
            isSafeToRetry,
            logger: loggers.shipment,
        });
        return false;
    }
    return true;
}

export async function controlNotBlockedOnHold(salesShipment: xtremSales.nodes.SalesShipment, isSafeToRetry: boolean) {
    if (
        xtremSales.functions.isBlockedOnHoldCustomer(
            await salesShipment.isOnHold,
            await (
                await (
                    await salesShipment.site
                ).legalCompany
            ).customerOnHoldCheck,
        )
    ) {
        xtremMasterData.functions.throwErrorOrLogWarn({
            errorMessage: salesShipment.$.context.localize(
                '@sage/xtrem-sales/cant_post_shipment_when_bill_to_customer_is_on_hold',
                'The Bill-to customer is on hold. The shipment cannot be posted.',
            ),
            isSafeToRetry,
            logger: loggers.shipment,
        });
        return false;
    }
    return true;
}

export async function controlShippingDate(salesShipment: xtremSales.nodes.SalesShipment, isSafeToRetry: boolean) {
    if ((await salesShipment.date) > date.today()) {
        xtremMasterData.functions.throwErrorOrLogWarn({
            errorMessage: salesShipment.$.context.localize(
                '@sage/xtrem-sales/cant_post_shipment_when_shipping_date_is_in_the_future',
                'The shipping date is in the future, the shipment cannot be posted.',
            ),
            isSafeToRetry,
            logger: loggers.shipment,
        });
        return false;
    }
    return true;
}

export async function controlAllLinesAllocated(salesShipment: xtremSales.nodes.SalesShipment, isSafeToRetry: boolean) {
    if (!(await xtremSales.nodes.SalesShipment.areAllShipmentLinesAllocated(salesShipment))) {
        xtremMasterData.functions.throwErrorOrLogWarn({
            errorMessage: salesShipment.$.context.localize(
                '@sage/xtrem-sales/cant_post_shipment_when_not_all_lines_are_allocated',
                'All the lines must be allocated to ship this document.',
            ),
            isSafeToRetry,
            logger: loggers.shipment,
        });
        return false;
    }
    return true;
}

/**
 * Checks if the sales shipment can be printed
 * @param context
 * @param document the document to check
 * @returns true if the sales shipment can be printed, false otherwise
 */
export async function checkDataBeforeActionExecution(
    context: Context,
    document: xtremSales.nodes.SalesShipment,
): Promise<boolean> {
    // Sales shipments have different validation logic than invoices/credit memos
    // Check basic document status
    const status = await document.status;
    if (!['readyToProcess', 'partiallyShipped', 'shipped'].includes(status)) {
        return false;
    }

    // Sales shipments don't have tax calculation status, so we skip that check
    // Check if all lines are allocated (shipment-specific validation)
    if (!(await xtremSales.nodes.SalesShipment.areAllShipmentLinesAllocated(document))) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/cant_post_shipment_when_not_all_lines_are_allocated',
                'All the lines must be allocated to ship this document.',
            ),
        );
    }

    return true; // Shipments don't use the SalesDocumentHooks pattern
}

/**
 * Gets email template data for sales shipment
 * @param document the sales shipment document
 * @returns email template data
 */
export async function getEmailTemplateData(document: xtremSales.nodes.SalesShipment) {
    const shipmentNumber = await document.number;
    const formattedShipmentDate = (await document.date).format('DD/MM/YYYY');

    return {
        shipment: {
            number: shipmentNumber,
            date: formattedShipmentDate,
            totalAmountIncludingTax: (await document.totalAmountExcludingTax).toString(),
            currencySymbol: await (await document.currency).symbol,
        },
        site: {
            name: await document.salesSite,
        },
    };
}

/**
 * Gets the subject for sales shipment emails
 * @param document the sales shipment document
 * @returns email subject
 */
export async function getSubject(document: xtremSales.nodes.SalesShipment): Promise<string> {
    return document.$.context.localize(
        '@sage/xtrem-sales/sales_shipment__email_subject',
        '{{shipmentSiteName}}: Shipment {{shipmentNumber}}',
        {
            shipmentSiteName: await (await document.site).name,
            shipmentNumber: await document.number,
        },
    );
}
