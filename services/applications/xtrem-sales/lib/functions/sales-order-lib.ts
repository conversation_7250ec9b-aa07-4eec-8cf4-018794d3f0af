import type { Collection, Context } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSales from '../index';
import { loggers } from './loggers';

const logger = Logger.getLogger(__filename, 'CreateTestData');

export async function getItemSiteAvailableStockQuantity(
    context: Context,
    item: xtremMasterData.nodes.Item,
    site: xtremSystem.nodes.Site,
): Promise<number> {
    if (item._id && site._id) {
        const itemSite = await context.tryRead(xtremMasterData.nodes.ItemSite, {
            item: item._id,
            site: site._id,
        });

        if (itemSite) {
            return (await itemSite.acceptedStockQuantity) - (await itemSite.allocatedQuantity);
        }
    }
    return 0;
}

export async function checkEmailSendable(
    context: Context,
    salesOrder: xtremSales.nodes.SalesOrder,
    contactEmail: string,
): Promise<void> {
    if (!contactEmail || !xtremSystem.sharedFunctions.validEmail(contactEmail)) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/nodes__sales_order__cannot_send_invalid_email',
                'The sales order cannot be sent. The email is not valid.',
            ),
        );
    }

    if (!['pending', 'quote'].includes(await salesOrder.status)) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/nodes__sales_order__cannot_send_invalid_status',
                'You can only send a "Quote" or a "Pending" sales order.',
            ),
        );
    }
    if ((await salesOrder.taxCalculationStatus) !== 'done' && (await salesOrder.taxEngine) !== 'avalaraAvaTax') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/nodes__sales_order__cannot_send_invalid_tax_status',
                'The tax calculation needs to be done before you can send the sales order.',
            ),
        );
    }
}

/**
 * Calculates the display status of the order based on the other document status properties
 * @param status
 * @param shippingStatus
 * @param taxCalculationStatus
 * @returns display status enum
 */
function calculateSalesOrderDisplayStatus(
    status: xtremSales.enums.SalesOrderStatus,
    shippingStatus: xtremSales.enums.SalesDocumentShippingStatus,
    taxCalculationStatus: xtremMasterData.enums.TaxCalculationStatus,
): xtremSales.enums.SalesOrderDisplayStatus {
    if (shippingStatus === 'shipped') {
        if (taxCalculationStatus === 'failed') {
            return 'taxCalculationFailed';
        }
        return 'shipped';
    }

    if (shippingStatus === 'partiallyShipped') {
        if (taxCalculationStatus === 'failed') {
            return 'taxCalculationFailed';
        }
        if (status === 'closed') {
            return 'closed';
        }
        return 'partiallyShipped';
    }

    if (status === 'closed') {
        return 'closed';
    }

    if (taxCalculationStatus === 'failed') {
        return 'taxCalculationFailed';
    }

    if (status === 'pending') {
        return 'confirmed';
    }
    return 'quote';
}

export async function setSalesOrderMail(
    context: Context,
    salesOrder: xtremSales.nodes.SalesOrder,
    data: xtremSales.interfaces.SalesOrderEmailTemplateData,
): Promise<xtremSales.interfaces.SalesOrderMail> {
    let subject;
    let emailTemplate;
    let filePrefix;

    if ((await salesOrder.status) === 'quote') {
        subject = context.localize(
            '@sage/xtrem-sales/sales_order_quote_email_subject',
            '{{salesOrderSiteName}}: Sales quote {{salesOrderNumber}}',
            {
                salesOrderSiteName: data.site.name,
                salesOrderNumber: data.salesOrder.number,
            },
        );

        emailTemplate = 'sales_order_quote_send';

        filePrefix = context.localize(
            '@sage/xtrem-sales/sales_order_quote_email_file_prefix',
            'Sales quote - {{salesOrderNumber}}',
            {
                salesOrderNumber: data.salesOrder.number,
            },
        );
    } else {
        subject = context.localize(
            '@sage/xtrem-sales/sales_order__email_subject',
            '{{salesOrderSiteName}}: Order {{salesOrderNumber}} confirmation',
            {
                salesOrderSiteName: data.site.name,
                salesOrderNumber: data.salesOrder.number,
            },
        );

        emailTemplate = 'sales_order_send';

        filePrefix = context.localize(
            '@sage/xtrem-sales/sales_order_email_file_prefix',
            'Sales order - {{salesOrderNumber}}',
            {
                salesOrderNumber: data.salesOrder.number,
            },
        );
    }

    return { subject, emailTemplate, filePrefix };
}

export async function updateWorkInProgressOnSalesOrderLineOpen(
    context: Context,
    salesOrderLine: xtremSales.nodes.SalesOrderLine,
) {
    const workInProgressId = (await salesOrderLine.workInProgress)?._id;
    if (workInProgressId) {
        const workInProgress = await context.read(
            xtremMasterData.nodes.WorkInProgress,
            { _id: workInProgressId },
            { forUpdate: true },
        );
        if ((await workInProgress.documentType) === 'salesOrder') {
            await workInProgress.$.set({
                remainingQuantityToAllocate: await salesOrderLine.remainingQuantityToAllocate,
            });
            await workInProgress.$.save();
        }
    }
}

export async function updateWorkInProgressOnSalesOrderOpen(context: Context, salesOrderNumber: string) {
    const salesOrder = await context.tryRead(
        xtremSales.nodes.SalesOrder,
        { number: salesOrderNumber },
        { forUpdate: true },
    );
    if (salesOrder) {
        await salesOrder.lines.forEach(async line => {
            await updateWorkInProgressOnSalesOrderLineOpen(context, line);
        });
    }
}

export async function updateWorkInProgressOnSalesOrderLineClose(
    context: Context,
    salesOrderLine: xtremSales.nodes.SalesOrderLine,
) {
    const workInProgressId = (await salesOrderLine.workInProgress)?._id;
    if (workInProgressId) {
        const workInProgress = await context.read(
            xtremMasterData.nodes.WorkInProgress,
            { _id: workInProgressId },
            { forUpdate: true },
        );
        if ((await workInProgress.documentType) === 'salesOrder') {
            await workInProgress.$.set({ remainingQuantityToAllocate: 0 });
            await workInProgress.$.save();
        }
    }
}

export async function updateWorkInProgressOnSalesOrderClose(context: Context, salesOrderNumber: string) {
    const salesOrder = await context.tryRead(
        xtremSales.nodes.SalesOrder,
        { number: salesOrderNumber },
        { forUpdate: true },
    );
    if (salesOrder) {
        await salesOrder.lines.forEach(async line => {
            await updateWorkInProgressOnSalesOrderLineClose(context, line);
        });
    }
}

export async function createTestSalesOrders(
    context: Context,
    orderCreation: xtremSales.interfaces.TestOrderCreationParameters,
): Promise<string> {
    const customer = await context.read(xtremMasterData.nodes.Customer, {
        businessEntity: `#${orderCreation.customerId}`,
    });

    const site = await (await orderCreation.item.itemSites.at(0))?.site;

    for (let i = 0; i < orderCreation.orderQuantity; i += 1) {
        let lineNumber = 1;
        if (orderCreation.fixedNumberOfLines) {
            lineNumber = orderCreation.numberOfLinesPerOrder;
        } else {
            lineNumber = xtremMasterData.functions.randomInteger(1, orderCreation.numberOfLinesPerOrder);
        }

        let itemNumber = xtremMasterData.functions.randomInteger(1, orderCreation.itemQuantity);
        let lineItem = await context.read(xtremMasterData.nodes.Item, {
            id: `${await orderCreation.item.id}-${itemNumber}`,
        });
        let itemDescription = `${await lineItem.description}`;
        const orderNumber = orderCreation.orderNumberRoot ?? 'MRPTEST';
        const newOrder = await context.create(xtremSales.nodes.SalesOrder, {
            number: `${orderNumber}-${i}`,
            site,
            date: date.today(),
            requestedDeliveryDate: orderCreation.randomDeliveryDate
                ? date.today().addDays(xtremMasterData.functions.randomInteger(0, 50))
                : date.today(),
            soldToCustomer: customer,
            status: 'pending',
            displayStatus: 'confirmed',
            lines: [
                {
                    status: 'pending',
                    item: lineItem,
                    itemDescription,
                    quantity: xtremMasterData.functions.randomInteger(1, 20),
                },
            ],
        });
        if (lineNumber > 1) {
            for (let l = 2; l < lineNumber; l += 1) {
                itemNumber = xtremMasterData.functions.randomInteger(1, orderCreation.itemQuantity);
                lineItem = await context.read(xtremMasterData.nodes.Item, {
                    id: `${await orderCreation.item.id}-${itemNumber}`,
                });
                itemDescription = `${await lineItem.description}`;
                await newOrder.lines.append({
                    status: 'pending',
                    item: lineItem,
                    itemDescription,
                    quantity: xtremMasterData.functions.randomInteger(1, 20),
                });
            }
        }
        await newOrder.$.save();
        logger.debug(() => `Are we allocating stock ${orderCreation.allocateStock}`);
        if (orderCreation.allocateStock) {
            logger.debug(() => 'Start of allocating sales order line');
            await newOrder.lines.forEach(async line => {
                const searchResults = await xtremStockData.functions.stockLib.searchStock(context, {
                    site: await line.stockSite,
                    item: await line.item,
                    activeQuantityInStockUnit: 0,
                });
                logger.debug(() => `Number of stock records found ${searchResults.length}`);
                if (searchResults.length > 0) {
                    const searchResult = searchResults.at(0);
                    logger.debug(
                        () =>
                            `Stock record found ${searchResult?.stockRecord._id} available ${searchResult?.quantityInStockUnit}`,
                    );
                    if (searchResult) {
                        const allocationResult = await xtremStockData.nodes.Stock.updateAllocations(
                            context,
                            {
                                documentLine:
                                    line as unknown as Promise<xtremStockData.interfaces.DocumentLineWithStockAllocation>,
                                allocationUpdates: [
                                    {
                                        action: 'create',
                                        stockRecord: context.read(xtremStockData.nodes.Stock, {
                                            _id: searchResult.stockRecord._id,
                                        }),
                                        quantity: await line.quantityInStockUnit,
                                    },
                                ],
                            },
                            { cannotOverAllocate: true },
                        );
                        logger.debug(() => `Allocation ${allocationResult[0].allocationRecord?._id}`);
                    }
                }
            });
        }
    }
    return `Number of items created - ${orderCreation.orderQuantity}`;
}

/**
 * Indicates if the sales order line can be automatically allocated
 * @param line sales order line to check
 * @returns true if this SO line should be considered in the automatic allocation
 */
export async function isSalesOrderLineAllocable(line: xtremSales.nodes.SalesOrderLine): Promise<boolean> {
    return (
        ['inProgress', 'pending'].includes(await line.status) &&
        (await (
            await line.item
        ).isStockManaged) &&
        (await line.assignments.length) === 0
    );
}

/**
 * Indicates if the sales order line should be taken into account for computing the header allocation status
 * @param line sales order line to check
 * @returns true if this SO line should be considered in the allocation status determination
 */
export async function isSalesOrderLineConsideredForAllocationStatus(
    line: xtremSales.nodes.SalesOrderLine,
): Promise<boolean> {
    return (await line.status) !== 'closed';
}

async function calculateSalesOrderShippingStatus(
    salesOrderLines: Collection<xtremSales.nodes.SalesOrderLine>,
): Promise<xtremSales.enums.SalesDocumentShippingStatus> {
    if ((await salesOrderLines.length) <= 0) {
        return 'notShipped';
    }
    if (await salesOrderLines.every(async line => (await line.shippingStatus) === 'shipped')) {
        return 'shipped';
    }
    if (
        (await salesOrderLines.filter(async line => ['shipped', 'partiallyShipped'].includes(await line.shippingStatus))
            .length) > 0
    ) {
        return 'partiallyShipped';
    }
    return 'notShipped';
}

async function calculateSalesOrderSalesInvoiceStatus(
    salesOrderLines: Collection<xtremSales.nodes.SalesOrderLine>,
): Promise<xtremSales.enums.SalesDocumentInvoiceStatus> {
    if ((await salesOrderLines.length) <= 0) {
        return 'notInvoiced';
    }
    if (await salesOrderLines.every(async line => (await line.invoiceStatus) === 'invoiced')) {
        return 'invoiced';
    }
    if (
        (await salesOrderLines.filter(async line =>
            ['invoiced', 'partiallyInvoiced'].includes(await line.invoiceStatus),
        ).length) > 0
    ) {
        return 'partiallyInvoiced';
    }
    return 'notInvoiced';
}

async function calculateSalesOrderStatus(
    salesOrderLines: Collection<xtremSales.nodes.SalesOrderLine>,
): Promise<xtremSales.enums.SalesOrderStatus> {
    if ((await salesOrderLines.length) <= 0) {
        return 'quote';
    }
    if (await salesOrderLines.every(async line => (await line.status) === 'closed')) {
        return 'closed';
    }
    if (await salesOrderLines.every(async line => ['pending', 'closed'].includes(await line.status))) {
        return 'pending';
    }
    if (await salesOrderLines.some(async line => (await line.status) === 'inProgress')) {
        return 'inProgress';
    }
    return 'quote';
}

export async function calculateSalesOrderStatuses(
    salesOrderLines: Collection<xtremSales.nodes.SalesOrderLine>,
    taxCalculationStatus: xtremMasterData.enums.TaxCalculationStatus,
) {
    const invoiceStatus = await calculateSalesOrderSalesInvoiceStatus(salesOrderLines);
    const shippingStatus = await calculateSalesOrderShippingStatus(salesOrderLines);
    const status = await calculateSalesOrderStatus(salesOrderLines);
    const displayStatus = calculateSalesOrderDisplayStatus(status, shippingStatus, taxCalculationStatus);
    return {
        invoiceStatus,
        shippingStatus,
        status,
        displayStatus,
    };
}

/**
 * Function that sets the lines statuses based on the previous and current state of the SO status
 * @param salesOrderInstance current state
 * @param old old state
 */
export async function fromQuoteToOrderUpdateLineStatus(
    salesOrderInstance: xtremSales.nodes.SalesOrder,
    old: xtremSales.nodes.SalesOrder,
) {
    if ((await old.status) === 'quote' && (await salesOrderInstance.status) === 'pending') {
        await salesOrderInstance.lines.forEach(async line => {
            if ((await line.status) !== 'closed') {
                await line.$.set({ status: 'pending' });
            }
        });
    }
}

export async function updateLineWorkInProgressIfItemStatusActive(
    salesOrderLineInstance: xtremSales.nodes.SalesOrderLine,
): Promise<void> {
    if ((await (await salesOrderLineInstance.item).status) === 'active') {
        await salesOrderLineInstance.$.set({ workInProgress: {} });
    }
}

export async function fromQuoteToPendingUpdateLineStatus(salesOrderLineInstance: xtremSales.nodes.SalesOrderLine) {
    if (
        (await (await salesOrderLineInstance.document).status) === 'pending' &&
        (await salesOrderLineInstance.status) === 'quote'
    ) {
        await salesOrderLineInstance.$.set({ status: 'pending' });
    }
}

export async function resetAllocationRequestStatusOnLineUpdate(
    salesOrderLineInstance: xtremSales.nodes.SalesOrderLine,
    old: xtremSales.nodes.SalesOrderLine,
) {
    if (['completed', 'error'].includes(await old.allocationRequestStatus)) {
        await salesOrderLineInstance.$.set({ allocationRequestStatus: 'noRequest' });
    }
}

export async function createSalesShipment(
    context: Context,
    salesOrder: xtremSales.nodes.SalesOrder,
    isSafeToRetry = false,
): Promise<xtremSales.nodes.SalesShipment[]> {
    // Process lines
    const salesShipmentsCreator = new xtremSales.classes.SalesShipmentsCreator(context);

    await salesOrder.lines
        .filter(async line => (await line.shippingStatus) !== 'shipped')
        .forEach(async line => {
            await salesShipmentsCreator.prepareNodeCreateData([{ salesDocumentLine: line }], {
                processAllShippableLines: true,
            });
        });

    let salesShipment: xtremSales.nodes.SalesShipment[] = [];

    // Create shipments
    if (Object.keys(salesShipmentsCreator.salesOutputDocuments).length !== 0) {
        salesShipment = (await salesShipmentsCreator.createSalesOutputDocuments())
            .documentsCreated as xtremSales.nodes.SalesShipment[];
    }

    // Check if there are errors - We only want to display the message on the first line
    // same that what was done on the page
    if (salesShipmentsCreator.lineErrors.length > 0) {
        const errorMessage = salesShipmentsCreator.lineErrors[0].message;
        throw new BusinessRuleError(errorMessage);
    }

    // throw an error if no shipments were created
    if (salesShipment.length === 0 && isSafeToRetry) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/nodes__sales_order__no_shipment_created',
                'No shipments were created for the sales order: {{salesOrderNumber}}.',
                { salesOrderNumber: await salesOrder.number },
            ),
        );
    }
    return salesShipment;
}

export async function controlSalesOrderLinesAllocated(
    salesOrder: xtremSales.nodes.SalesOrder,
    isSafeToRetry = false,
): Promise<boolean> {
    if (
        await salesOrder.lines.some(
            async line =>
                (await (await line.item).isStockManaged) &&
                !['notAllocated', 'notManaged'].includes(await line.allocationStatus),
        )
    ) {
        xtremMasterData.functions.throwErrorOrLogWarn({
            errorMessage: salesOrder.$.context.localize(
                '@sage/xtrem-sales/nodes__sales_order__cannot_close_order_quantity_allocated',
                'Remove the stock allocation before closing the order.',
            ),
            isSafeToRetry,
            logger: loggers.order,
        });
        return false;
    }
    return true;
}

export async function controlAllocationRequestStatus(
    salesOrder: xtremSales.nodes.SalesOrder,
    isSafeToRetry = false,
): Promise<boolean> {
    if ((await salesOrder.allocationRequestStatus) === 'inProgress') {
        xtremMasterData.functions.throwErrorOrLogWarn({
            errorMessage: salesOrder.$.context.localize(
                '@sage/xtrem-sales/nodes__sales_order__cannot_close_allocated_request_in_progress',
                'You can only close the sales order after the allocation request is complete for the lines.',
            ),
            isSafeToRetry,
            logger: loggers.order,
        });
        return false;
    }
    return true;
}

export async function controlClosedStatus(
    salesOrder: xtremSales.nodes.SalesOrder,
    isSafeToRetry = false,
): Promise<boolean> {
    if ((await salesOrder.status) === 'closed') {
        xtremMasterData.functions.throwErrorOrLogWarn({
            errorMessage: salesOrder.$.context.localize(
                '@sage/xtrem-sales/nodes__sales_order__sales_order_is_already_closed',
                'The sales order is closed. You cannot close the line.',
            ),
            isSafeToRetry,
            logger: loggers.order,
        });
        return false;
    }
    return true;
}

export async function controlOrderToOrder(
    salesOrder: xtremSales.nodes.SalesOrder,
    isSafeToRetry = false,
): Promise<boolean> {
    if (await salesOrder.isOrderAssignmentLinked) {
        xtremMasterData.functions.throwErrorOrLogWarn({
            errorMessage: salesOrder.$.context.localize(
                '@sage/xtrem-sales/nodes__sales_order__cannot_close_order_because_of_a_link',
                'Remove the supply order links before closing the sales order.',
            ),
            isSafeToRetry,
            logger: loggers.order,
        });
        return false;
    }
    return true;
}

export async function controlOrderToOrderQuantity(salesOrder: xtremSales.nodes.SalesOrder): Promise<void> {
    const { sumActualQuantity, sumQuantityInStockUnit, filteredAssignedOrders } =
        await xtremSales.nodes.SalesOrder.orderAssignmentQuantitySum(salesOrder.$.context, salesOrder);
    if (
        filteredAssignedOrders !== null &&
        filteredAssignedOrders.length > 0 &&
        sumActualQuantity !== sumQuantityInStockUnit
    ) {
        throw new BusinessRuleError(
            salesOrder.$.context.localize(
                '@sage/xtrem-sales/nodes__sales_order__cannot_ship_order_quantity_linked_is_not_equal',
                'The supply order quantities and sales order quantities need to be the same. You cannot ship the sales order.',
            ),
        );
    }
}

export async function controlInactiveItems(salesOrder: xtremSales.nodes.SalesOrder): Promise<void> {
    if (await salesOrder.lines.some(async line => (await (await line.item).status) !== 'active')) {
        throw new BusinessRuleError(
            salesOrder.$.context.localize(
                '@sage/xtrem-sales/nodes__sales_order__cannot_create_shipment',
                'You need to remove the inactive items before you change the document status.',
            ),
        );
    }
}

export function validateOrderHasLines(context: Context, linesCount: number): void {
    if (linesCount === 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/nodes__sales_order__print_no_lines',
                'There are no lines on this document. You can print the document after you add a line.',
            ),
        );
    }
}

export function validateTaxCalculation(context: Context, taxStatus: xtremMasterData.enums.TaxCalculationStatus): void {
    if (taxStatus === 'failed') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/nodes__sales_order__print_tax_calculation_failed',
                'The tax calculation for this document failed. You need to correct the tax issues before you print the document.',
            ),
        );
    }
}

export async function validateLineItemActive(context: Context, line: xtremSales.nodes.SalesOrderLine): Promise<void> {
    const item = await line.item;
    const itemStatus = await item.status;

    if (itemStatus !== 'active') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/nodes__sales_order__print_inactive_items',
                'The document contains inactive items. You need to remove the inactive items before you print the document.',
            ),
        );
    }
}

export async function checkPreferredProcess(
    salesOrderLine: xtremSales.nodes.SalesOrderLine,
    preferredProcess?: xtremMasterData.enums.PreferredProcess,
) {
    const itemSite = await salesOrderLine.itemSite;
    return !!(
        itemSite &&
        (await itemSite.isOrderToOrder) &&
        (!preferredProcess || (await itemSite.preferredProcess) === preferredProcess)
    );
}

export function getOrderAssignments(parameters: {
    line: xtremSales.nodes.SalesOrderLine;
    demandType: xtremStockData.enums.OrderToOrderDemandType;
    supplyType?: xtremStockData.enums.OrderToOrderSupplyType;
}): Promise<xtremStockData.nodes.OrderAssignment[]> {
    return parameters.line.$.context
        .query(xtremStockData.nodes.OrderAssignment, {
            filter: {
                demandDocumentLine: { _eq: parameters.line._id },
                demandType: { _eq: parameters.demandType },
                supplyType: { _eq: parameters.supplyType },
            },
        })
        .toArray();
}

/**
 * Checks if the sales order can be printed
 * @param context
 * @param document the document to check
 * @returns true if the sales order can be printed, false otherwise
 */
export async function checkDataBeforeActionExecution(
    context: Context,
    document: xtremSales.nodes.SalesOrder,
): Promise<boolean> {
    // Sales orders have different validation logic than invoices/credit memos
    // Check basic document status and validation
    const status = await document.status;
    if (!['draft', 'confirmed', 'partiallyShipped', 'shipped'].includes(status)) {
        return false;
    }

    // Check tax calculation status
    if ((await document.taxCalculationStatus) === 'failed') {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-sales/functions__sales_order_lib__tax_calculation_failed',
                'You need to resolve tax calculation issues to print the document: {{salesDocumentNumber}}.',
                { salesDocumentNumber: await document.number },
            ),
        );
    }

    // Use sales order specific validation
    return xtremSales.nodes.SalesOrder.beforePrintSalesOrder(context, document);
}

/**
 * Gets email template data for sales order
 * @param document the sales order document
 * @returns email template data
 */
export async function getEmailTemplateData(document: xtremSales.nodes.SalesOrder) {
    const orderNumber = await document.number;
    const formattedOrderDate = (await document.date).format('DD/MM/YYYY');

    return {
        order: {
            number: orderNumber,
            date: formattedOrderDate,
            totalAmountIncludingTax: (await document.totalAmountIncludingTax).toString(),
            currencySymbol: await (await document.currency).symbol,
        },
        site: {
            name: await document.salesSite,
        },
    };
}

/**
 * Gets the subject for sales order emails
 * @param document the sales order document
 * @returns email subject
 */
export async function getSubject(document: xtremSales.nodes.SalesOrder): Promise<string> {
    return document.$.context.localize(
        '@sage/xtrem-sales/sales_order__email_subject',
        '{{orderSiteName}}: Sales Order {{orderNumber}}',
        {
            orderSiteName: await (await document.site).name,
            orderNumber: await document.number,
        },
    );
}
