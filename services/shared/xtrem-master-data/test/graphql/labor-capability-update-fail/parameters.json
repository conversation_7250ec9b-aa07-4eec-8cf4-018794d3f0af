{"Update labor capability fail with incorrect date range": {"comment": "this unit test is skipped because the expected error format doesn't fit the actual error format", "executionMode": "skip", "input": {"properties": {"_id": "1", "id": "Machine", "name": "Changed name", "dateRangeValidity": "[2020-02-15,2020-01-29]", "capabilityLevel": "2"}}, "output": {"errors": [{"locations": [{"column": 99, "line": 4}], "message": "Expected value of type \"DateRange\", found \"[2020-02-15,2020-01-29]\"; Invalid range: included start 2020-02-15 should not be greater than excluded end 2020-01-30"}]}}}