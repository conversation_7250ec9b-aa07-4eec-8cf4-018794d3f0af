import { BusinessRuleError, asyncArray } from '@sage/xtrem-core';
import type * as xtremMailer from '@sage/xtrem-mailer';
import * as xtremMasterData from '..';
import { BaseDocument } from '../nodes/base-document';

export async function printDocumentAndEmail(
    document: BaseDocument,
    mail: { to: xtremMasterData.nodes.ContactBase[]; subject: string; body: string; reportName: string },
) {
    const context = document.$.context;

    if (!(await document.canPrint)) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-master-data/nodes__base_document__cannot_print_document',
                'The document cannot be printed.',
            ),
        );
    }
    const reports = await xtremMasterData.functions.reportGeneration(
        context,
        mail.reportName,
        '',
        document.getReportVariables(),
    );
    if (reports.length === 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-master-data/nodes__base_document__nothing_to_send',
                'The document cannot be sent. No report has been created.',
            ),
        );
    }
    // There we are not managing multiple recipients

    const emailTemplateData = await document.getEmailTemplateData(mail.to[0]);

    // email recipients
    const recipients: xtremMailer.interfaces.Recipients = {
        to: await asyncArray(mail.to)
            .map(async contact => ({
                address: await contact.email,
                name: await contact.displayName,
            }))
            .toArray(),
    };

    await xtremMasterData.functions.sendReportsByMail(context, {
        templateName: mail.reportName,
        templateData: emailTemplateData,
        subject: mail.subject,
        recipients,
        filePrefix: `${document.$.factory.getLocalizedTitle(document.$.context)} ${await document.number} `,
        reports,
        node: document,
    });

    await document.setIsPrintedIsSent();

    return true;
}
