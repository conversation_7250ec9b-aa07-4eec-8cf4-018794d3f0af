import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3Purchasing from '..';

const denormalized: Denormalized = { maxRepeat: 3 };

const joins: Joins<PurchaseOrderFromSupplierAddressLines> = {
    referenceJoins: {
        _denormalizedParent: {
            id: 'id',
        },
    },
};

@decorators.node<PurchaseOrderFromSupplierAddressLines>({
    storage: 'external',
    tableName: 'PORDER',
    keyPropertyNames: ['denormalizedIndex', 'id'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class PurchaseOrderFromSupplierAddressLines extends Node {
    @decorators.integerProperty<PurchaseOrderFromSupplierAddressLines, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<PurchaseOrderFromSupplierAddressLines, 'id'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'POHNUM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<PurchaseOrderFromSupplierAddressLines, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3Purchasing.nodes.PurchaseOrder,
    })
    readonly _denormalizedParent: Reference<sageX3Purchasing.nodes.PurchaseOrder>;

    @decorators.stringProperty<PurchaseOrderFromSupplierAddressLines, 'addressLine'>({
        isPublished: true,
        isStored: true,
        columnName: 'BPAADDLIG',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly addressLine: Promise<string>;
}
