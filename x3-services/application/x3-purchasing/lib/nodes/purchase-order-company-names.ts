import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3Purchasing from '..';

const denormalized: Denormalized = { maxRepeat: 2 };

const joins: Joins<PurchaseOrderCompanyNames> = {
    referenceJoins: {
        _denormalizedParent: {
            id: 'id',
        },
    },
};

@decorators.node<PurchaseOrderCompanyNames>({
    storage: 'external',
    tableName: 'PORDER',
    keyPropertyNames: ['denormalizedIndex', 'id'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class PurchaseOrderCompanyNames extends Node {
    @decorators.integerProperty<PurchaseOrderCompanyNames, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<PurchaseOrderCompanyNames, 'id'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'POHNUM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<PurchaseOrderCompanyNames, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3Purchasing.nodes.PurchaseOrder,
    })
    readonly _denormalizedParent: Reference<sageX3Purchasing.nodes.PurchaseOrder>;

    @decorators.stringProperty<PurchaseOrderCompanyNames, 'companyName'>({
        isPublished: true,
        isStored: true,
        columnName: 'BPRNAM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly companyName: Promise<string>;
}
