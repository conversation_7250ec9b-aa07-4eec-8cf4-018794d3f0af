{
    x3MasterData {
        bomHeader {
            query(filter: "{ bomCode: { code: '5'}, parentProduct: { code: 'I-PARENT-01' } }") {
                edges {
                    node {
                        parentProduct {
                            code
                        }
                        bomCode {
                            code
                        }
                        accessCode {
                            access
                        }
                        localizedDescription
                        useStatus
                        isPrototype
                        validityStartDate
                        validityEndDate
                        reviewLevel
                        baseQuantity
                        managementUnit
                        bomType
                        journalNumberConfig
                        linkedDocuments
                        details {
                            query {
                                edges {
                                    node {
                                        sequence
                                        componentProduct {
                                            code
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
