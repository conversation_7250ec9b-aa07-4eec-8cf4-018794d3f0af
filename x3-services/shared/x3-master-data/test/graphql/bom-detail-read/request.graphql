{
    x3MasterData {
        bomDetail {
            query(filter: "{ parentProduct: { code: 'I-PARENT-01' } }") {
                edges {
                    node {
                        parentProduct {
                            code
                        }
                        bomCode {
                            parentProduct {
                                code
                            }
                            bomCode {
                                code
                            }
                        }
                        componentProduct {
                            code
                        }
                        unit {
                            code
                        }
                        sequence
                        sequenceRemainder
                        componentType
                        linkDescription
                        bomType
                        firstValidLot
                        lastValidLot
                        validityStartDate
                        validityEndDate
                        linkQuantityType
                        quantityRounding
                        stockUnitConversionFactor
                        linkQuantityInBomUnit
                        linkQuantity
                        scrapFactorPercentage
                        routingOperation
                        routingOperationSuffix
                        operationLeadTime
                        isPrintMaterialNote
                        isPrintAcknowledgment
                        isPrintPackingSlip
                        isPrintInvoice
                        linkReviewIndex
                        isValuation
                        pickListCode
                        weighingPositiveTolerancePercentage
                        weighingNegativeTolerancePercentage
                        setupLevel
                        typeOfSupply
                        selectionFormula
                        quantityFormula
                        majorVersion
                        minorVersion
                        revisionGroup
                    }
                }
            }
        }
    }
}
