import * as sageX3System from '@sage/x3-system';
import { decorators, Node, Reference, date, decimal, Collection, TextStream } from '@sage/xtrem-core';
import { X3StorageManager, Joins } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3MasterData from '..';

const joins: Joins<BomHeader> = {
    referenceJoins: {
        parentProduct: {
            code: 'parentProduct',
        },
        bomCode: {
            bomType: 'bomType',
            code: 'bomCode',
        },
        accessCode: {
            access: 'accessCode',
        },
        headerTextRef: {
            code: 'headerTextKey',
        },
    },
    localizedStrings: {
        localizedDescription: {
            tableName() {
                return 'BOM';
            },
            columnName() {
                return 'BOMDESAXX';
            },
            key1: ['parentProduct', 'bomCode'],
            key2: ['bomType'],
        },
    },
    collectionJoins: {
        details: {
            bomCode: 'bomCode',
            parentProduct: 'parentProduct',
            bomType: 'bomType',
        },
    },
};

const compositeReferences = {
    headerTextRef: {
        headerText: 'text',
    },
};

@decorators.node<BomHeader>({
    storage: 'external',
    tableName: 'BOM',
    keyPropertyNames: ['parentProduct', 'bomCode', 'bomType'],
    indexes: [
        {
            orderBy: {
                parentProduct: 1,
                bomCode: 1,
                bomType: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    externalStorageManager: new X3StorageManager({
        joins,
        compositeReferences,
        joinFallbackProperties: ['bomType'],
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class BomHeader extends Node {
    @decorators.referenceProperty<BomHeader, 'parentProduct'>({
        isPublished: true,
        isStored: true,
        columnName: 'ITMREF',
        columnType: 'string',
        node: () => sageX3MasterData.nodes.Product,
        lookupAccess: true,
    })
    readonly parentProduct: Reference<sageX3MasterData.nodes.Product>;

    @decorators.referenceProperty<BomHeader, 'bomCode'>({
        isPublished: true,
        isStored: true,
        columnName: 'BOMALT',
        columnType: 'integer',
        node: () => sageX3MasterData.nodes.BomCodes,
        lookupAccess: true,
    })
    readonly bomCode: Reference<sageX3MasterData.nodes.BomCodes>;

    @decorators.enumProperty<BomHeader, 'bomType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'BOMALTTYP',
        dataType: () => sageX3MasterData.enums.bomCodeTypeDatatype,
        lookupAccess: true,
    })
    readonly bomType: Promise<sageX3MasterData.enums.BomCodeType | null>;

    @decorators.stringProperty<BomHeader, 'localizedDescription'>({
        isPublished: true,
        isStored: true,
        columnName: 'BOMDESAXX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.translatableTextDatatype,
    })
    readonly localizedDescription: Promise<string>;

    @decorators.stringProperty<BomHeader, 'identifier1'>({
        isStored: true,
        columnName: 'IDENT1',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly identifier1: Promise<string>;

    @decorators.enumProperty<BomHeader, 'useStatus'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'USESTA',
        dataType: () => sageX3MasterData.enums.useStatusDatatype,
    })
    readonly useStatus: Promise<sageX3MasterData.enums.UseStatus | null>;

    @decorators.booleanProperty<BomHeader, 'isPrototype'>({
        isPublished: true,
        isStored: true,
        columnName: 'NPIPRO',
        serviceOptions: () => [sageX3System.serviceOptions.NpiActivityCode],
    })
    readonly isPrototype: Promise<boolean>;

    @decorators.dateProperty<BomHeader, 'validityStartDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'BOHSTRDAT',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly validityStartDate: Promise<date | null>;

    @decorators.dateProperty<BomHeader, 'validityEndDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'BOHENDDAT',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly validityEndDate: Promise<date | null>;

    @decorators.stringProperty<BomHeader, 'reviewLevel'>({
        isPublished: true,
        isStored: true,
        columnName: 'BOMRLE',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly reviewLevel: Promise<string>;

    @decorators.decimalProperty<BomHeader, 'baseQuantity'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'BASQTY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly baseQuantity: Promise<decimal | null>;

    @decorators.enumProperty<BomHeader, 'managementUnit'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'QTYCOD',
        dataType: () => sageX3MasterData.enums.managementUnitDatatype,
    })
    readonly managementUnit: Promise<sageX3MasterData.enums.ManagementUnit | null>;

    @decorators.stringProperty<BomHeader, 'headerTextKey'>({
        isStored: true,
        columnName: 'HEATEX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly headerTextKey: Promise<string>;

    @decorators.stringProperty<BomHeader, 'journalNumberConfig'>({
        isPublished: true,
        isStored: true,
        columnName: 'CFGVCRNUM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
        serviceOptions: () => [sageX3System.serviceOptions.CfgActivityCode],
    })
    readonly journalNumberConfig: Promise<string>;

    @decorators.referenceProperty<BomHeader, 'accessCode'>({
        isPublished: true,
        isStored: true,
        provides: ['accessCode'],
        isNullable: true,
        columnName: 'ACSCOD',
        columnType: 'string',
        node: () => sageX3System.nodes.Access,
    })
    readonly accessCode: Reference<sageX3System.nodes.Access | null>;

    @decorators.stringProperty<BomHeader, 'linkedDocuments'>({
        isPublished: true,
        isStored: true,
        columnName: 'PLMATTURL',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly linkedDocuments: Promise<string>;

    @decorators.collectionProperty<BomHeader, 'details'>({
        isPublished: true,
        node: () => sageX3MasterData.nodes.BomDetail,
        isMutable: true,
        dependsOn: ['bomCode', 'parentProduct', 'bomType'],
        lookupAccess: true,
    })
    readonly details: Collection<sageX3MasterData.nodes.BomDetail>;

    @decorators.referenceProperty<BomHeader, 'headerTextRef'>({
        isStored: true,
        isNullable: true,
        columnType: 'string',
        node: () => sageX3MasterData.nodes.CommonText,
    })
    readonly headerTextRef: Reference<sageX3MasterData.nodes.CommonText | null>;

    @decorators.textStreamProperty<BomHeader, 'headerText'>({
        isPublished: true,
        isStored: true,
        columnName: 'TEXTE',
    })
    readonly headerText: Promise<TextStream>;
}
