import * as sageX3System from '@sage/x3-system';
import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3MasterData from '..';

const joins: Joins<BomCodes> = {
    referenceJoins: {
        accessCode: {
            access: 'accessCode',
        },
        site: {
            code: 'site',
        },
    },
};

@decorators.node<BomCodes>({
    storage: 'external',
    tableName: 'TABBOMALT',
    keyPropertyNames: ['bomType', 'code'],
    indexes: [
        {
            orderBy: {
                bomType: 1,
                code: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    externalStorageManager: new X3StorageManager({
        joins,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class BomCodes extends Node {
    @decorators.enumProperty<BomCodes, 'bomType'>({
        isPublished: true,
        isStored: true,
        columnName: 'BOMALTTYP',
        dataType: () => sageX3MasterData.enums.bomCodeTypeDatatype,
        lookupAccess: true,
    })
    readonly bomType: Promise<sageX3MasterData.enums.BomCodeType>;

    @decorators.integerProperty<BomCodes, 'code'>({
        isPublished: true,
        isStored: true,
        columnName: 'BOMALT',
        lookupAccess: true,
    })
    readonly code: Promise<integer>;

    @decorators.stringProperty<BomCodes, 'description'>({
        isPublished: true,
        isStored: true,
        columnName: 'TBODESAXX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.translatableTextDatatype,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<BomCodes, 'shortDescription'>({
        isPublished: true,
        isStored: true,
        columnName: 'TBOSHOAXX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.translatableTextDatatype,
    })
    readonly shortDescription: Promise<string>;

    @decorators.referenceProperty<BomCodes, 'accessCode'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'ACSCOD',
        columnType: 'string',
        node: () => sageX3System.nodes.Access,
    })
    readonly accessCode: Reference<sageX3System.nodes.Access | null>;

    @decorators.referenceProperty<BomCodes, 'site'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'FCY',
        columnType: 'string',
        node: () => sageX3System.nodes.Site,
    })
    readonly site: Reference<sageX3System.nodes.Site | null>;

    @decorators.booleanProperty<BomCodes, 'isProductionUtilization'>({
        isPublished: true,
        isStored: true,
        columnName: 'MFGUSE',
    })
    readonly isProductionUtilization: Promise<boolean>;

    @decorators.booleanProperty<BomCodes, 'isCostUtilization'>({
        isPublished: true,
        isStored: true,
        columnName: 'CSTUSE',
    })
    readonly isCostUtilization: Promise<boolean>;

    @decorators.booleanProperty<BomCodes, 'isMrpUtilization'>({
        isPublished: true,
        isStored: true,
        columnName: 'MRPUSE',
    })
    readonly isMrpUtilization: Promise<boolean>;

    @decorators.booleanProperty<BomCodes, 'isMpsUtilization'>({
        isPublished: true,
        isStored: true,
        columnName: 'MPSUSE',
    })
    readonly isMpsUtilization: Promise<boolean>;
}
