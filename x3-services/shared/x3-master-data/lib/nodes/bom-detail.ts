import * as sageX3System from '@sage/x3-system';
import { decorators, Node, Reference, integer, date, decimal, TextStream } from '@sage/xtrem-core';
import { X3StorageManager, Joins } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3MasterData from '..';

const joins: Joins<BomDetail> = {
    referenceJoins: {
        parentProduct: {
            code: 'parentProduct',
        },
        bomCode: {
            parentProduct: 'parentProduct',
            bomCode: 'bomCode',
            bomType: 'bomType',
        },
        componentProduct: {
            code: 'componentProduct',
        },
        unit: {
            code: 'unit',
        },
        bomTextRef: {
            code: 'bomTextKey',
        },
    },
};

const compositeReferences = {
    bomTextRef: {
        bomText: 'text',
    },
};

@decorators.node<BomDetail>({
    storage: 'external',
    tableName: 'BOMD',
    keyPropertyNames: ['parentProduct', 'bomCode', 'sequence', 'componentProduct', 'bomType'],
    indexes: [
        {
            orderBy: {
                parentProduct: 1,
                bomCode: 1,
                sequence: 1,
                componentProduct: 1,
                bomType: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    externalStorageManager: new X3StorageManager({
        joins,
        compositeReferences,
        joinFallbackProperties: ['componentProduct'],
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class BomDetail extends Node {
    @decorators.referenceProperty<BomDetail, 'parentProduct'>({
        isPublished: true,
        isStored: true,
        columnName: 'ITMREF',
        columnType: 'string',
        node: () => sageX3MasterData.nodes.Product,
        lookupAccess: true,
    })
    readonly parentProduct: Reference<sageX3MasterData.nodes.Product>;

    @decorators.referenceProperty<BomDetail, 'bomCode'>({
        isPublished: true,
        isStored: true,
        columnName: 'BOMALT',
        columnType: 'integer',
        node: () => sageX3MasterData.nodes.BomHeader,
        lookupAccess: true,
    })
    readonly bomCode: Reference<sageX3MasterData.nodes.BomHeader>;

    @decorators.integerProperty<BomDetail, 'sequence'>({
        isPublished: true,
        isStored: true,
        columnName: 'BOMSEQ',
        lookupAccess: true,
    })
    readonly sequence: Promise<integer>;

    @decorators.referenceProperty<BomDetail, 'componentProduct'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CPNITMREF',
        columnType: 'string',
        node: () => sageX3MasterData.nodes.Product,
        lookupAccess: true,
    })
    readonly componentProduct: Reference<sageX3MasterData.nodes.Product | null>;

    @decorators.enumProperty<BomDetail, 'bomType'>({
        isPublished: true,
        isStored: true,
        columnName: 'BOMALTTYP',
        dataType: () => sageX3MasterData.enums.bomCodeTypeDatatype,
        lookupAccess: true,
    })
    readonly bomType: Promise<sageX3MasterData.enums.BomCodeType>;

    @decorators.integerProperty<BomDetail, 'sequenceRemainder'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'BOMSEQNUM',
    })
    readonly sequenceRemainder: Promise<integer | null>;

    @decorators.enumProperty<BomDetail, 'componentType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CPNTYP',
        dataType: () => sageX3MasterData.enums.componentTypeDatatype,
    })
    readonly componentType: Promise<sageX3MasterData.enums.ComponentType | null>;

    @decorators.stringProperty<BomDetail, 'linkDescription'>({
        isPublished: true,
        isStored: true,
        columnName: 'BOMSHO',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly linkDescription: Promise<string>;

    @decorators.stringProperty<BomDetail, 'firstValidLot'>({
        isPublished: true,
        isStored: true,
        columnName: 'BOMSTRLOT',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly firstValidLot: Promise<string>;

    @decorators.stringProperty<BomDetail, 'lastValidLot'>({
        isPublished: true,
        isStored: true,
        columnName: 'BOMENDLOT',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly lastValidLot: Promise<string>;

    @decorators.dateProperty<BomDetail, 'validityStartDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'BOMSTRDAT',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly validityStartDate: Promise<date | null>;

    @decorators.dateProperty<BomDetail, 'validityEndDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'BOMENDDAT',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly validityEndDate: Promise<date | null>;

    @decorators.enumProperty<BomDetail, 'linkQuantityType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'LIKQTYCOD',
        dataType: () => sageX3MasterData.enums.bomQuantityLinkDatatype,
    })
    readonly linkQuantityType: Promise<sageX3MasterData.enums.BomQuantityLink | null>;

    @decorators.enumProperty<BomDetail, 'quantityRounding'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'QTYRND',
        dataType: () => sageX3MasterData.enums.componentRoundedDatatype,
    })
    readonly quantityRounding: Promise<sageX3MasterData.enums.ComponentRounded | null>;

    @decorators.referenceProperty<BomDetail, 'unit'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'BOMUOM',
        columnType: 'string',
        node: () => sageX3MasterData.nodes.UnitOfMeasure,
    })
    readonly unit: Reference<sageX3MasterData.nodes.UnitOfMeasure | null>;

    @decorators.decimalProperty<BomDetail, 'stockUnitConversionFactor'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'BOMSTUCOE',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly stockUnitConversionFactor: Promise<decimal | null>;

    @decorators.decimalProperty<BomDetail, 'linkQuantityInBomUnit'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'BOMQTY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly linkQuantityInBomUnit: Promise<decimal | null>;

    @decorators.decimalProperty<BomDetail, 'linkQuantity'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'LIKQTY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly linkQuantity: Promise<decimal | null>;

    @decorators.decimalProperty<BomDetail, 'scrapFactorPercentage'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'SCA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly scrapFactorPercentage: Promise<decimal | null>;

    @decorators.integerProperty<BomDetail, 'routingOperation'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CPNOPE',
    })
    readonly routingOperation: Promise<integer | null>;

    @decorators.integerProperty<BomDetail, 'routingOperationSuffix'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'OPENUMLEV',
    })
    readonly routingOperationSuffix: Promise<integer | null>;

    @decorators.integerProperty<BomDetail, 'operationLeadTime'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'BOMOFS',
    })
    readonly operationLeadTime: Promise<integer | null>;

    @decorators.booleanProperty<BomDetail, 'isPrintMaterialNote'>({
        isPublished: true,
        isStored: true,
        columnName: 'PICPRN',
    })
    readonly isPrintMaterialNote: Promise<boolean>;

    @decorators.booleanProperty<BomDetail, 'isPrintAcknowledgment'>({
        isPublished: true,
        isStored: true,
        columnName: 'OCNPRN',
    })
    readonly isPrintAcknowledgment: Promise<boolean>;

    @decorators.booleanProperty<BomDetail, 'isPrintPackingSlip'>({
        isPublished: true,
        isStored: true,
        columnName: 'NDEPRN',
    })
    readonly isPrintPackingSlip: Promise<boolean>;

    @decorators.booleanProperty<BomDetail, 'isPrintInvoice'>({
        isPublished: true,
        isStored: true,
        columnName: 'INVPRN',
    })
    readonly isPrintInvoice: Promise<boolean>;

    @decorators.stringProperty<BomDetail, 'linkReviewIndex'>({
        isPublished: true,
        isStored: true,
        columnName: 'LIKRLE',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly linkReviewIndex: Promise<string>;

    @decorators.stringProperty<BomDetail, 'bomTextKey'>({
        isStored: true,
        columnName: 'BOMTEXNUM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly bomTextKey: Promise<string>;

    @decorators.booleanProperty<BomDetail, 'isValuation'>({
        isPublished: true,
        isStored: true,
        columnName: 'CSTFLG',
    })
    readonly isValuation: Promise<boolean>;

    @decorators.enumProperty<BomDetail, 'pickListCode'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'PKC',
        dataType: () => sageX3MasterData.enums.codeToServeDatatype,
        serviceOptions: () => [sageX3System.serviceOptions.MwmActivityCode],
    })
    readonly pickListCode: Promise<sageX3MasterData.enums.CodeToServe | null>;

    @decorators.decimalProperty<BomDetail, 'weighingPositiveTolerancePercentage'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'ITMTOLPOS',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
        serviceOptions: () => [sageX3System.serviceOptions.MwmActivityCode],
    })
    readonly weighingPositiveTolerancePercentage: Promise<decimal | null>;

    @decorators.decimalProperty<BomDetail, 'weighingNegativeTolerancePercentage'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'ITMTOLNEG',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
        serviceOptions: () => [sageX3System.serviceOptions.MwmActivityCode],
    })
    readonly weighingNegativeTolerancePercentage: Promise<decimal | null>;

    @decorators.enumProperty<BomDetail, 'setupLevel'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'LEVSET',
        dataType: () => sageX3MasterData.enums.toleranceDefinitionLevelDatatype,
        serviceOptions: () => [sageX3System.serviceOptions.MwmActivityCode],
    })
    readonly setupLevel: Promise<sageX3MasterData.enums.ToleranceDefinitionLevel | null>;

    @decorators.enumProperty<BomDetail, 'typeOfSupply'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'SCOFLG',
        dataType: () => sageX3MasterData.enums.materialReplenishTypeDatatype,
    })
    readonly typeOfSupply: Promise<sageX3MasterData.enums.MaterialReplenishType | null>;

    @decorators.stringProperty<BomDetail, 'selectionFormula'>({
        isPublished: true,
        isStored: true,
        columnName: 'FORSEL',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly selectionFormula: Promise<string>;

    @decorators.stringProperty<BomDetail, 'quantityFormula'>({
        isPublished: true,
        isStored: true,
        columnName: 'FORQTY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly quantityFormula: Promise<string>;

    @decorators.stringProperty<BomDetail, 'majorVersion'>({
        isPublished: true,
        isStored: true,
        columnName: 'ECCVALMAJ',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
        serviceOptions: () => [sageX3System.serviceOptions.EccActivityCode],
    })
    readonly majorVersion: Promise<string>;

    @decorators.stringProperty<BomDetail, 'minorVersion'>({
        isPublished: true,
        isStored: true,
        columnName: 'ECCVALMIN',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
        serviceOptions: () => [sageX3System.serviceOptions.EccActivityCode],
    })
    readonly minorVersion: Promise<string>;

    @decorators.integerProperty<BomDetail, 'revisionGroup'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'ECCRLEGRP',
        serviceOptions: () => [sageX3System.serviceOptions.EccActivityCode],
    })
    readonly revisionGroup: Promise<integer | null>;

    @decorators.referenceProperty<BomDetail, 'bomTextRef'>({
        isStored: true,
        isNullable: true,
        columnType: 'string',
        node: () => sageX3MasterData.nodes.CommonText,
    })
    readonly bomTextRef: Reference<sageX3MasterData.nodes.CommonText | null>;

    @decorators.textStreamProperty<BomDetail, 'bomText'>({
        isPublished: true,
        isStored: true,
        columnName: 'TEXTE',
    })
    readonly bomText: Promise<TextStream>;
}
